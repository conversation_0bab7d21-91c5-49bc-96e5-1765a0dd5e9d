"use client";

import { <PERSON>, <PERSON>Off } from "lucide-react";
import Image from "next/image";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox, Form, InputField, Label } from "@/components/ui/form";
import { cn } from "@/lib/utils";

import { useCreateAccountPasswordVisibility } from "./hooks/use-password-visibility";
import { emailPasswordSchema, type EmailPasswordStepProps } from "./types";

/**
 * EmailPasswordStep component for create account flow
 * Handles email and password input with terms acceptance
 */
export function EmailPasswordStep({
  isLoading,
  isLoaded,
  onSubmit,
}: EmailPasswordStepProps) {
  const {
    showPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
  } = useCreateAccountPasswordVisibility();

  return (
    <div className="flex w-1/2 flex-col gap-5">
      <div className="flex flex-col items-center justify-center space-y-8 text-center">
        <Image
          src="/images/authentication/light-logo.svg"
          alt="logo"
          width={346}
          height={40}
        />
        <div className="space-y-2">
          <h1 className="min-h-16 text-[32px] font-semibold leading-[150%]">
            Create Your Account
          </h1>
          <p className="text-gray-600">
            Enter your email and create a secure password to get started
          </p>
        </div>
      </div>

      <Form
        schema={emailPasswordSchema}
        onSubmit={onSubmit}
        defaultValues={{
          email: "",
          password: "",
          confirmPassword: "",
          acceptTerms: false,
        }}
        isSubmitting={isLoading}
        mode="onChange"
        formProps={{ shouldFocusError: false }}
        className="flex flex-col gap-5"
      >
        <div>
          <Label htmlFor="email" required>
            Email Address
          </Label>
          <InputField
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            placeholder="Type your email"
          />
        </div>

        <div>
          <Label htmlFor="password" required>
            Password
          </Label>
          <div className="relative">
            <InputField
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              autoComplete="new-password"
              placeholder="Your password"
              className="pr-10"
            />
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className={cn(
                "absolute right-0 top-3 flex items-center pr-3",
                "text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100",
                "disabled:cursor-not-allowed disabled:opacity-50",
              )}
              aria-label={showPassword ? "Hide password" : "Show password"}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 scale-x-[-1]" aria-hidden="true" />
              ) : (
                <Eye className="h-5 w-5" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        <div>
          <Label htmlFor="confirmPassword" required>
            Confirm Password
          </Label>
          <div className="relative">
            <InputField
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              autoComplete="new-password"
              placeholder="Confirm your password"
              className="pr-10"
            />
            <button
              type="button"
              onClick={toggleConfirmPasswordVisibility}
              className={cn(
                "absolute right-0 top-3 flex items-center pr-3",
                "text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100",
                "disabled:cursor-not-allowed disabled:opacity-50",
              )}
              aria-label={
                showConfirmPassword ? "Hide password" : "Show password"
              }
            >
              {showConfirmPassword ? (
                <EyeOff className="h-5 w-5 scale-x-[-1]" aria-hidden="true" />
              ) : (
                <Eye className="h-5 w-5" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        <div className="flex items-center gap-2 pb-5">
          <Checkbox id="accept-terms" name="acceptTerms" />
          <Label
            htmlFor="accept-terms"
            className="text-sm font-medium leading-5"
          >
            You agree to our{" "}
            <a
              href="/terms-of-service"
              target="_blank"
              rel="noopener noreferrer"
              className="text-purple-600 underline-offset-4 hover:text-purple-700 hover:underline"
            >
              Terms & Conditions
            </a>
            .
          </Label>
        </div>

        <Button
          type="submit"
          isLoading={isLoading}
          disabled={!isLoaded}
          disabledForInvalid
          className="w-full"
        >
          {isLoading ? "Creating Account..." : "Create Account"}
        </Button>
      </Form>
    </div>
  );
}
