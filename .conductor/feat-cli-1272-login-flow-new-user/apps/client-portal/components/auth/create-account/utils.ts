import toast from "react-hot-toast";

/**
 * Handles API errors consistently across the create account flow
 */
export function handleCreateAccountApiError(
  error: any,
  formHandler: any,
  field?: string,
) {
  console.error("Create account API error:", error);

  if (error.response?.data) {
    const responseData = error.response.data;

    // Handle validation errors
    if (responseData.errors || responseData.message) {
      const errorMessage = responseData.message || "Validation failed";

      if (field && formHandler) {
        formHandler.setError(field, {
          message: errorMessage,
        });
      } else {
        toast.error(errorMessage);
      }
      return;
    }
  }

  // Handle network errors
  if (
    error.code === "NETWORK_ERROR" ||
    error.message?.includes("Network Error")
  ) {
    toast.error("Network error. Please check your connection and try again.");
    return;
  }

  // Handle timeout errors
  if (error.code === "ECONNABORTED") {
    toast.error("Request timed out. Please try again.");
    return;
  }

  // Generic error handling
  const errorMessage =
    error.response?.data?.message ||
    error.message ||
    "An unexpected error occurred. Please try again.";

  if (field && formHandler) {
    formHandler.setError(field, {
      message: errorMessage,
    });
  } else {
    toast.error(errorMessage);
  }
}

/**
 * Handles OTP verification errors specifically
 */
export function handleCreateAccountOTPVerificationError(
  error: any,
): string | null {
  return error.message || "Failed to verify code. Please try again.";
}
