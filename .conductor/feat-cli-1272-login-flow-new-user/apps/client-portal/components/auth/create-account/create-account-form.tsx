"use client";

import { useSignIn } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";

import { Link } from "@/components/shared/link";
import {
  useSelfSignupConfirm,
  useSelfSignupRequest,
  useSelfSignupResendOtp,
} from "@/lib/apis/auth/hooks";
import { CREATE_ACCOUNT_COOLDOWN_SECONDS } from "@/lib/auth/constants";

import { SwitchCountries } from "../components/countries-selection";
import { GoBack } from "../components/go-back";
import { EmailPasswordStep } from "./email-password-step";
import { useCreateAccountResendCooldown } from "./hooks/use-resend-cooldown";
import { SuccessStep } from "./success-step";
import type { CreateAccountFormStep, EmailPasswordFormData } from "./types";
import {
  handleCreateAccountApiError,
  handleCreateAccountOTPVerificationError,
} from "./utils";
import { VerificationStep } from "./verification-step";

/**
 * CreateAccountForm component orchestrates the three-step account creation flow:
 * 1. Email and password entry
 * 2. OTP verification
 * 3. Success message and automatic sign-in
 */
export function CreateAccountForm() {
  const { isLoaded, signIn, setActive } = useSignIn();
  const router = useRouter();

  // State management
  const [currentStep, setCurrentStep] =
    useState<CreateAccountFormStep>("email-password");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [otpError, setOtpError] = useState<string | null>(null);

  // Resend cooldown functionality
  const { cooldown: resendCooldown, startCooldown } =
    useCreateAccountResendCooldown();

  // React Query mutations
  const selfSignupRequest = useSelfSignupRequest();
  const selfSignupConfirm = useSelfSignupConfirm();
  const selfSignupResendOtp = useSelfSignupResendOtp();

  // Calculate loading state from mutations
  const isLoading =
    selfSignupRequest.isPending ||
    selfSignupConfirm.isPending ||
    selfSignupResendOtp.isPending;

  // Step 1: Email and password submission handler
  const handleEmailPasswordSubmit = async (
    data: EmailPasswordFormData,
    formHandler: any,
  ) => {
    if (!isLoaded) return;

    setEmail(data.email);
    setPassword(data.password);

    try {
      await selfSignupRequest.mutateAsync({
        email: data.email,
        password: data.password,
      });
      setCurrentStep("verification");
    } catch (error: any) {
      handleCreateAccountApiError(error, formHandler);
    }
  };

  // Step 2: OTP verification handler
  const handleOTPSubmit = async () => {
    if (!isLoaded || verificationCode.length !== 6 || !email || !password)
      return;

    try {
      const result = await selfSignupConfirm.mutateAsync({
        email,
        otp: verificationCode,
        password,
      });

      if (result.success) {
        setCurrentStep("success");
      } else {
        setOtpError(result.message || "Invalid verification code");
      }
    } catch (error: any) {
      const errorMessage = handleCreateAccountOTPVerificationError(error);
      if (errorMessage) {
        setOtpError(errorMessage);
      }
    }
  };

  // Session activation handler for success step
  const handleSessionActivation = async () => {
    if (!isLoaded || !email || !password) return;

    try {
      const signInResult = await signIn.create({
        identifier: email,
        password,
      });

      if (signInResult.status === "complete") {
        await setActive({
          session: signInResult.createdSessionId,
          redirectUrl: "/",
        });
      } else {
        // If sign-in is not complete, redirect to sign-in page
        router.replace("/authentication/sign-in");
      }
    } catch (error: any) {
      console.error("Auto sign-in error:", error);
      router.replace("/authentication/sign-in");
    }
  };

  // Resend verification code handler
  const handleResendCode = async () => {
    if (!isLoaded || resendCooldown > 0 || !email) return;

    try {
      await selfSignupResendOtp.mutateAsync({ email });
      startCooldown(CREATE_ACCOUNT_COOLDOWN_SECONDS);
    } catch (error: any) {
      console.error("Resend code error:", error);
      toast.error("Failed to resend code. Please try again.");
    }
  };

  const handleGoBack = () => {
    if (currentStep === "email-password") {
      // Navigate back to sign-in page
      router.replace("/authentication/sign-in");
    } else if (currentStep === "success") {
      // Don't allow going back from success step
      return;
    } else {
      // Reset form to email-password step
      setCurrentStep("email-password");
      setEmail("");
      setPassword("");
      setVerificationCode("");
      setOtpError(null);
    }
  };

  const renderForm = () => {
    switch (currentStep) {
      case "email-password":
        return (
          <EmailPasswordStep
            isLoading={isLoading}
            isLoaded={isLoaded}
            email={email}
            onSubmit={handleEmailPasswordSubmit}
          />
        );

      case "verification":
        return (
          <VerificationStep
            isLoading={isLoading}
            isLoaded={isLoaded}
            email={email}
            verificationCode={verificationCode}
            setVerificationCode={setVerificationCode}
            onSubmit={handleOTPSubmit}
            onResendCode={handleResendCode}
            resendCooldown={resendCooldown}
            otpError={otpError}
            setOtpError={setOtpError}
          />
        );

      case "success":
        return (
          <SuccessStep
            isLoading={isLoading}
            isLoaded={isLoaded}
            email={email}
            password={password}
            onActivateSession={handleSessionActivation}
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      <div className="flex w-full items-center justify-between">
        {currentStep !== "success" ? (
          <GoBack onGoBack={handleGoBack} />
        ) : (
          <div></div>
        )}

        <SwitchCountries />
      </div>

      {renderForm()}

      {currentStep === "email-password" && (
        <div className="text-center text-sm">
          <span>Already have an account? </span>
          <Link
            href="/authentication/sign-in"
            className="text-purple-600 underline-offset-4 hover:text-purple-700 hover:underline"
          >
            Sign in
          </Link>
        </div>
      )}
    </>
  );
}
